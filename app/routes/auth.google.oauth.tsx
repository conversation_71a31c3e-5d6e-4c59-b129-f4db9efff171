/**
 * 传统 Google OAuth 登录路由
 * 使用 Client Secret 的服务器端 OAuth 流程
 * 这是 Google One Tap 的备用方案
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { exchangeCodeForTokens, handleGoogleAuth } from "~/lib/auth/google.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const error = url.searchParams.get("error");

  // 如果用户拒绝授权
  if (error) {
    console.log("Google OAuth error:", error);
    return redirect("/auth/login?error=oauth_denied");
  }

  // 如果没有授权码，重定向到 Google OAuth
  if (!code) {
    const clientId = context.cloudflare?.env?.GOOGLE_CLIENT_ID;
    if (!clientId) {
      return redirect("/auth/login?error=no_client_id");
    }

    // 构建 Google OAuth URL
    const redirectUri = `${url.origin}/auth/google/oauth`;
    const scope = "openid email profile";
    const state = "random_state_string"; // 在生产环境中应该使用随机生成的 state

    const googleAuthUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");
    googleAuthUrl.searchParams.set("client_id", clientId);
    googleAuthUrl.searchParams.set("redirect_uri", redirectUri);
    googleAuthUrl.searchParams.set("response_type", "code");
    googleAuthUrl.searchParams.set("scope", scope);
    googleAuthUrl.searchParams.set("state", state);
    googleAuthUrl.searchParams.set("access_type", "offline"); // 获取 refresh token
    googleAuthUrl.searchParams.set("prompt", "consent"); // 强制显示同意页面

    return redirect(googleAuthUrl.toString());
  }

  // 处理授权码
  try {
    const clientId = context.cloudflare?.env?.GOOGLE_CLIENT_ID;
    const clientSecret = context.cloudflare?.env?.GOOGLE_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      return redirect("/auth/login?error=missing_credentials");
    }

    const redirectUri = `${url.origin}/auth/google/oauth`;

    // 交换授权码获取 tokens
    const tokens = await exchangeCodeForTokens(code, clientId, clientSecret, redirectUri);

    if (tokens.error || !tokens.id_token) {
      console.error("Token exchange failed:", tokens.error);
      return redirect("/auth/login?error=token_exchange_failed");
    }

    // 使用 ID token 进行认证
    const authResult = await handleGoogleAuth(tokens.id_token, request, context.cloudflare?.env);

    if (!authResult.success) {
      console.error("Google auth failed:", authResult.error);
      return redirect(`/auth/login?error=${encodeURIComponent(authResult.error || "auth_failed")}`);
    }

    // 创建认证 cookie
    const isSecure = new URL(request.url).protocol === "https:";
    const authCookie = `auth-token=${authResult.accessToken}; Path=/; Max-Age=${7 * 24 * 60 * 60}; HttpOnly; SameSite=Lax${isSecure ? "; Secure" : ""}`;

    // 设置认证 cookie 并重定向
    const headers = new Headers();
    headers.append("Set-Cookie", authCookie);

    return redirect("/console", { headers });
  } catch (error) {
    console.error("Google OAuth error:", error);
    return redirect("/auth/login?error=oauth_error");
  }
}

// 处理 POST 请求
export async function action({ request, context }: LoaderFunctionArgs) {
  return loader({ request, context });
}
