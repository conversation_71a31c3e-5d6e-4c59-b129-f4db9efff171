import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { useLoaderData } from "@remix-run/react";
import { Database, Gauge, HardDrive, Palette } from "lucide-react";
import { PerformanceDashboard } from "~/components/dev/performance-dashboard";
import UnifiedLayout from "~/components/layout/unified-layout";

import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

import { getPageTitle } from "~/lib/seo/meta-utils";

export async function loader({ request }: LoaderFunctionArgs) {
  const title = "Development Test Suite";
  const description = "Test various development features and integrations.";

  // Mock status data for now
  const dbStatus = { connected: true, error: null };
  const blobStatus = { connected: true, error: null };

  return json({
    meta: {
      title,
      description,
    },

    dbStatus,
    blobStatus,
  });
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  if (!data) {
    return [
      { title: "Development Test Suite" },
      { name: "description", content: "Test various development features and integrations." },
    ];
  }
  const title = data.meta?.title ?? "Development Test Suite";
  const description =
    data.meta?.description ?? "Test various development features and integrations.";
  return [{ title: getPageTitle(title) }, { name: "description", content: description }];
};

export default function TestSuitePage() {
  const data = useLoaderData<typeof loader>();

  return (
    <UnifiedLayout
      headerProps={{}}
      hero={{
        badge: { text: "Comprehensive Testing", variant: "default" },
        title: "Development Test Suite",
        description:
          "Unified page for testing themes, database, R2 storage, performance, and more.",
      }}
    >
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Database Testing */}
          <Card className="shadow-lg transition-shadow hover:shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="mr-2 h-6 w-6 text-primary" />
                Database Testing
              </CardTitle>
              <CardDescription>Test database connection and query functionality.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <span>Connection Status:</span>
                <Badge variant={data.dbStatus.connected ? "default" : "destructive"}>
                  {data.dbStatus.connected ? "Connected" : "Not Connected"}
                </Badge>
              </div>
              {data.dbStatus.connected && (
                <div className="flex items-center gap-2">
                  <span>User Count:</span>
                  <Badge variant="outline">{data.dbStatus.userCount}</Badge>
                </div>
              )}
              {data.dbStatus.error && (
                <div className="mt-2 rounded-lg border border-destructive/20 bg-destructive/10 p-3">
                  <p className="text-sm text-destructive">Error: {data.dbStatus.error}</p>
                </div>
              )}
              <div className="mt-2 rounded-lg bg-muted p-3">
                <p className="text-sm">
                  {data.dbStatus.connected
                    ? "✅ Database connection is normal. Data operations can be performed."
                    : "❌ Database not connected. Please check the DATABASE_URL environment variable."}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* R2 Storage Testing */}
          <Card className="shadow-lg transition-shadow hover:shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center">
                <HardDrive className="mr-2 h-6 w-6 text-primary" />
                R2 Storage Testing
              </CardTitle>
              <CardDescription>Test R2 storage connection and basic operations.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <span>Connection Status:</span>
                <Badge variant={data.r2Status.connected ? "default" : "destructive"}>
                  {data.r2Status.connected ? "Connected" : "Not Connected"}
                </Badge>
              </div>
              {data.r2Status.error && (
                <div className="mt-2 rounded-lg border border-destructive/20 bg-destructive/10 p-3">
                  <p className="text-sm text-destructive">Error: {data.r2Status.error}</p>
                </div>
              )}
              <div className="mt-2 rounded-lg bg-muted p-3">
                <p className="text-sm">
                  {data.r2Status.connected
                    ? "✅ R2 Storage connection is normal."
                    : "❌ R2 Storage not connected. Please check R2 binding and environment variables."}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Performance Testing */}
          <Card className="col-span-1 shadow-lg transition-shadow hover:shadow-xl md:col-span-2 lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Gauge className="mr-2 h-6 w-6 text-primary" />
                Performance Testing
              </CardTitle>
              <CardDescription>
                Monitor Core Web Vitals and other performance metrics.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PerformanceDashboard />
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
